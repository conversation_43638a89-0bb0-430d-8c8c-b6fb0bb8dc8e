mutation CreateClaim($input: ClaimInput!) {
  createClaim(input: $input) {
    _id
    id
    createdAt
    updatedAt
    status
    claimType
    claimData {
      ... on AllowanceClaim {
        amount
        purpose
        from
        to
        workingHours
        receipts
      }
      ... on TravelClaim {
        amount
        purpose
        from
        to
        client
        toll
        distance
        receipts
      }
      ... on ExpenseClaim {
        amount
        purpose
        items
        date
        receipts
      }
      ... on SiteClaim {
        amount
        purpose
        items
        receipts
        site {
          _id
          id
          createdAt
          updatedAt
          name
          description
          address
          emergencyContact
        }
      }
    }
    rejectedReason
  }
}
