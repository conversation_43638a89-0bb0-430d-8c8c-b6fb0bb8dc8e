fragment ShiftFragment on Shift {
  id
  recurringId
  startDateTime
  endDateTime
  location {
    name
    id
  }
  users {
    fullname
    id
  }
}

query Shift($shiftId: String!) {
  shift(id: $shiftId) {
    ...ShiftFragment
  }
}

query Shifts($input: ShiftsInput!) {
  shifts(shiftsInput: $input) {
    ...ShiftFragment
  }
}

query GetShiftsByUser($userId: String!) {
  shiftsByUser(userId: $userId) {
    ...ShiftFragment
  }
}

mutation ClockIn($clockInInput: ClockInInput!) {
  clockIn(clockInInput: $clockInInput) {
    id
  }
}

mutation ClockOut($clockOut: ClockOutInput!) {
  clockOut(clockOutInput: $clockOut) {
    id
  }
}
