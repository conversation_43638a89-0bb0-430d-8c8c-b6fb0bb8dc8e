import React from 'react';
import { StyleSheet, View, Text, FlatList } from 'react-native';
import { Colors } from '@/constants/Colors';
import { AnnouncementItem, Announcement } from '@/components/AnnouncementItem';

// Sample data for announcements (same as in index.tsx)
const announcements: Announcement[] = [
  {
    id: '1',
    sender: {
      name: '<PERSON><PERSON><PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    },
    message: 'Is this jacket waterproof and warm enough for winter?',
    time: '01:09 am',
    isUnread: true,
  },
  {
    id: '2',
    sender: {
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    },
    message: 'Do you have any new arrivals in medium size?',
    time: '01:08 pm',
    isUnread: true,
  },
  {
    id: '3',
    sender: {
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/53.jpg',
    },
    message: 'I need a pair of comfortable jeans for everyday wear.',
    time: '06:32 pm',
    isUnread: true,
  },
  {
    id: '4',
    sender: {
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/91.jpg',
    },
    message: "I'm attending a wedding soon. Do you have formal wear?",
    time: '08:20 pm',
  },
  {
    id: '5',
    sender: {
      name: 'Wade Warren',
      avatar: 'https://randomuser.me/api/portraits/men/41.jpg',
    },
    message: 'What are your best-selling accessories this season?',
    time: '10:32 pm',
  },
  {
    id: '6',
    sender: {
      name: 'Kathryn Murphy',
      avatar: 'https://randomuser.me/api/portraits/women/64.jpg',
    },
    message: "I'm looking for a cozy sweater. What do you recommend?",
    time: '04:15 am',
  },
  {
    id: '7',
    sender: {
      name: 'Leslie Alexander',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    },
    message: 'Do you have any new arrivals in medium size?',
    time: '01:08 pm',
  },
  {
    id: '8',
    sender: {
      name: 'Floyd Miles',
      avatar: 'https://randomuser.me/api/portraits/men/53.jpg',
    },
    message: 'I need a pair of comfortable jeans for everyday wear.',
    time: '06:32 pm',
  },
];

export default function AnnouncementsScreen() {
  const handleAnnouncementPress = (announcement: Announcement) => {
    console.log('Announcement pressed:', announcement.id);
    // Handle announcement press
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={announcements}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <AnnouncementItem 
            announcement={item} 
            onPress={handleAnnouncementPress} 
          />
        )}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  listContent: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    margin: 16,
    overflow: 'hidden',
  },
});
