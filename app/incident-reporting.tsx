import React, { useState } from "react";
import { StyleSheet, View, Alert } from "react-native";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Colors } from "@/constants/Colors";
import {
  Form,
  FormInput,
  FormDatePicker,
  FormSubmitButton,
  FormImagePicker,
  FormSelect,
} from "@/components/forms";
import {
  incidentReportSchema,
  IncidentReportFormData,
  defaultIncidentReportValues,
  getPriorityLevelOptions,
} from "@/schemas/incident-reporting";

export default function IncidentReportingScreen() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<IncidentReportFormData>({
    defaultValues: defaultIncidentReportValues,
    resolver: zodResolver(incidentReportSchema),
    mode: "onChange",
  });

  // No header, so no back button handler needed

  // Handle form submission
  const onSubmit = (data: IncidentReportFormData) => {
    setIsSubmitting(true);
    console.log("Form data:", data);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      Alert.alert("Success", "Incident report submitted successfully!", [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]);
    }, 1500);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />

      <Form contentContainerStyle={styles.formContent}>
        {/* Date Input */}
        <FormDatePicker
          name="date"
          control={control}
          label="Date"
          placeholder="dd-mm-yy"
          minDate={
            new Date(new Date().setFullYear(new Date().getFullYear() - 1))
              .toISOString()
              .split("T")[0]
          } // 1 year ago
          maxDate={new Date().toISOString().split("T")[0]} // Today
        />

        {/* Location Input */}
        <FormInput
          name="location"
          control={control}
          label="Location"
          placeholder="Select"
        />

        {/* Description Input */}
        <FormInput
          name="description"
          control={control}
          label="Description"
          placeholder="Type something..."
          multiline
          numberOfLines={4}
        />

        {/* Type Input */}
        <FormInput
          name="type"
          control={control}
          label="Type"
          placeholder="Enter"
        />

        {/* Priority Level Dropdown */}
        <FormSelect
          name="priorityLevel"
          control={control}
          label="Priority Level"
          placeholder="Select"
          options={getPriorityLevelOptions()}
        />

        {/* Upload Evidence */}
        <FormImagePicker
          name="evidence"
          control={control}
          label="Upload Evidence"
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Submit"
          onSubmit={handleSubmit(onSubmit)}
          isSubmitting={isSubmitting}
          isValid={isValid}
          style={styles.submitButton}
        />
      </Form>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  formContent: {
    padding: 16,
    paddingBottom: 80, // Increased bottom padding
    paddingTop: 20,
  },
  submitButton: {
    marginTop: 20,
  },
});
