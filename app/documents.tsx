import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSequence,
  withSpring,
  Easing,
} from "react-native-reanimated";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { StatusBar } from "expo-status-bar";
import { useRouter } from "expo-router";
import { hexToRGBA } from "@/constants/Colors";

// Sample data for document folders
const documentFolders = [
  { id: "1", name: "Jakarta", count: 18 },
  { id: "2", name: "<PERSON><PERSON><PERSON>", count: 20 },
  { id: "3", name: "Surakarta", count: 42 },
  { id: "4", name: "Yogjakarta", count: 28 },
  { id: "5", name: "<PERSON><PERSON>", count: 30 },
  { id: "6", name: "Holiday<PERSON>", count: 68 },
  { id: "7", name: "Reports", count: 15 },
  { id: "8", name: "Certificates", count: 12 },
];

// Calculate grid dimensions
const { width } = Dimensions.get("window");
const PADDING = 16;
const COLUMN_GAP = 16;
const NUM_COLUMNS = 2;
const ITEM_WIDTH =
  (width - PADDING * 2 - COLUMN_GAP * (NUM_COLUMNS - 1)) / NUM_COLUMNS;

interface FolderItemProps {
  name: string;
  count: number;
  onPress: () => void;
  index: number;
}

/**
 * Folder item component with animations using Reanimated
 */
function FolderItem({ name, count, onPress, index }: FolderItemProps) {
  // Animation values - similar to MenuGrid
  const scale = useSharedValue(0.3);
  const translateY = useSharedValue(40);
  const opacity = useSharedValue(0);

  // Start animations when component mounts
  React.useEffect(() => {
    // Minimal staggered delay - just enough to create a wave effect
    const delay = index * 50;

    // Very quick fade in - completes in just 150ms
    opacity.value = withDelay(
      delay,
      withTiming(1, { duration: 150, easing: Easing.in(Easing.ease) })
    );

    // Icon animation - more bouncy spring with minimal delay
    scale.value = withDelay(
      delay,
      withSpring(1, { damping: 8, stiffness: 80, mass: 1.2 })
    );

    // Slide up animation with minimal delay
    translateY.value = withDelay(
      delay,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
  }, []);

  // Function to handle press animation
  const handlePress = () => {
    // Create a more pronounced pulse effect
    scale.value = withSequence(
      withTiming(0.85, { duration: 100, easing: Easing.inOut(Easing.quad) }),
      withSpring(1.1, { damping: 5, stiffness: 200 }),
      withSpring(1, { damping: 15 })
    );

    // Add a small bounce
    translateY.value = withSequence(
      withTiming(-5, { duration: 100 }),
      withSpring(0, { damping: 5, stiffness: 200 })
    );

    // Call the onPress callback after a short delay
    setTimeout(() => onPress(), 100);
  };

  // Animated styles
  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }, { translateY: translateY.value }],
  }));

  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        style={styles.folderItem}
        onPress={handlePress}
        activeOpacity={0.9}
      >
        <View style={styles.folderIconContainer}>
          <View style={styles.folderIconTop} />
          <View style={styles.folderIconBase}>
            <View style={styles.folderLine} />
          </View>
        </View>
        <Text style={styles.folderName}>{name}</Text>
        <Text style={styles.folderCount}>({count})</Text>
      </TouchableOpacity>
    </Animated.View>
  );
}

export default function DocumentsScreen() {
  const router = useRouter();

  // Animation values for FAB
  const fabScale = useSharedValue(0);
  const fabTranslateY = useSharedValue(100);

  // Animate FAB entrance
  React.useEffect(() => {
    // Delay FAB animation until folders have animated in
    const delay = 600;

    // Scale animation with elastic effect
    fabScale.value = withDelay(
      delay,
      withSpring(1, { damping: 8, stiffness: 80, mass: 1 })
    );

    // Slide up animation
    fabTranslateY.value = withDelay(
      delay,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
  }, []);

  // FAB animated style
  const fabAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: fabScale.value }, { translateY: fabTranslateY.value }],
  }));

  const handleFolderPress = (folder: {
    id: string;
    name: string;
    count: number;
  }) => {
    console.log(`Folder pressed: ${folder.name}`);
    // Navigate to folder details screen
    router.push({ pathname: "/documents/[id]", params: { id: folder.id } });
  };

  const handleBackPress = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Documents</Text>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <FlatList
          data={documentFolders}
          keyExtractor={(item) => item.id}
          numColumns={NUM_COLUMNS}
          columnWrapperStyle={styles.row}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          renderItem={({ item, index }) => (
            <FolderItem
              name={item.name}
              count={item.count}
              onPress={() => handleFolderPress(item)}
              index={index}
            />
          )}
        />
      </View>

      {/* Animated FAB */}
      <Animated.View style={[styles.fab, fabAnimatedStyle]}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => console.log("Add new document")}
        >
          <Ionicons name="add" size={24} color={Colors.white} />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  listContent: {
    paddingHorizontal: PADDING,
    paddingBottom: 20,
  },
  row: {
    justifyContent: "space-between",
    marginBottom: 16,
  },
  folderItem: {
    width: ITEM_WIDTH,
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
    aspectRatio: 1,
  },
  folderIconContainer: {
    width: 60,
    height: 50,
    marginBottom: 12,
    position: "relative",
  },
  folderIconTop: {
    position: "absolute",
    top: 0,
    left: 10,
    width: 20,
    height: 10,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    backgroundColor: "#78A5F8",
    zIndex: 1,
  },
  folderIconBase: {
    position: "absolute",
    top: 5,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 8,
    backgroundColor: "#4169E1",
    justifyContent: "center",
    alignItems: "center",
  },
  folderLine: {
    width: "60%",
    height: 4,
    backgroundColor: "#78A5F8",
    borderRadius: 2,
  },
  folderName: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 4,
    textAlign: "center",
  },
  folderCount: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
});
