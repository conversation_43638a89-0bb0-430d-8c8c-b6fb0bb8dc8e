import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { useAuth } from "@/hooks/useAuth";
import { useSession } from "@/providers/auth-provider";

// Define types for the AccountOption component
interface AccountOptionProps {
  icon: keyof typeof Ionicons.glyphMap;
  label: string;
  onPress: () => void;
}

// Account option item component
function AccountOption({ icon, label, onPress }: AccountOptionProps) {
  return (
    <TouchableOpacity
      style={styles.optionItem}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.optionIconContainer}>
        <Ionicons name={icon} size={22} color={Colors.primary} />
      </View>
      <Text style={styles.optionLabel}>{label}</Text>
      <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
    </TouchableOpacity>
  );
}

export default function AccountScreen() {
  const router = useRouter();
  const { signOut } = useSession();

  // Handle option press
  const handleOptionPress = (optionName: string) => {
    console.log(`Option pressed: ${optionName}`);

    // Navigate based on the option selected
    if (optionName === "Incident Reporting") {
      // Use type assertion to fix TypeScript error
      // This is safe because we've added the screen to _layout.tsx
      router.push("/incident-reporting" as any);
    } else if (optionName === "Uniform Request") {
      router.push("/uniform-request" as any);
    } else if (optionName === "Claims") {
      router.push("/claims" as any);
    } else if (optionName === "Sign Out") {
      Alert.alert("Sign Out", "Are you sure you want to sign out?", [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Sign Out",
          onPress: () => {
            signOut();
            router.replace("/login");
          },
          style: "destructive",
        },
      ]);
    }
    // Add more navigation options as needed
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* User account section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>User account</Text>
        <View style={styles.optionsList}>
          <AccountOption
            icon="person-outline"
            label="Personal Information"
            onPress={() => handleOptionPress("Personal Information")}
          />
          <AccountOption
            icon="warning-outline"
            label="Incident Reporting"
            onPress={() => handleOptionPress("Incident Reporting")}
          />
          <AccountOption
            icon="shirt-outline"
            label="Uniform Request"
            onPress={() => handleOptionPress("Uniform Request")}
          />
          <AccountOption
            icon="cash-outline"
            label="Claims"
            onPress={() => handleOptionPress("Claims")}
          />
          <AccountOption
            icon="help-circle-outline"
            label="Help & Support"
            onPress={() => handleOptionPress("Help & Support")}
          />
          <AccountOption
            icon="log-out-outline"
            label="Sign Out"
            onPress={() => handleOptionPress("Sign Out")}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  optionsList: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    marginHorizontal: 16,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: Colors.border,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  optionIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${Colors.primary}15`,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  optionLabel: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
  },
});
