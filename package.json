{"name": "kkpm-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "codegen": "graphql-codegen --watch", "get-schema": "node ./scripts/get-schema.js", "codegen-once": "graphql-codegen", "codegen:live": "node ./scripts/switch-codegen.js live", "codegen:temp": "node ./scripts/switch-codegen.js temp"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@hookform/resolvers": "^5.0.1", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@tanstack/react-query": "^4", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo": "~52.0.46", "expo-blur": "~14.0.3", "expo-camera": "~16.0.18", "expo-constants": "~17.0.8", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "^16.0.6", "expo-linking": "~7.0.5", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "graphql": "^16.10.0", "graphql-request": "^5", "jotai": "^2.12.3", "jwt-decode": "^4.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.0", "react-native": "0.76.9", "react-native-calendars": "^1.1311.0", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "zod": "^3.24.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "^4.2.6", "@graphql-codegen/typescript-react-query": "^6.1.0", "@parcel/watcher": "^2.4.1", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "graphql-codegen-typescript-client": "0.18.2", "graphql-codegen-typescript-common": "0.18.2", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}