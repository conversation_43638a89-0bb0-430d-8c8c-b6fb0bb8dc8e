import { CodegenConfig } from "@graphql-codegen/cli";

const config: CodegenConfig = {
  schema: "http://localhost:4000/graphql",
  documents: ["./graphql/**/*.gql"],
  ignoreNoDocuments: true, // for better experience with the watcher
  generates: {
    "./generated/graphql.ts": {
      plugins: [
        "typescript",
        "typescript-operations",
        "typescript-react-query",
      ],
      config: {
        isReactHook: false,
        fetcher: "../client#fetchData",
        errorType: "Error",
        exposeDocument: true,
        exposeQueryKeys: true,
        scalars: {
          DateTime: "Date",
          JSON: "any",
        },
      },
    },
  },
};

export default config;
