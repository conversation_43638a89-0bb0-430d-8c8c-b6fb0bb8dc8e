import { useMutation, useQuery, UseMutationOptions, UseQueryOptions } from '@tanstack/react-query';
import { fetchData } from '../client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: Date; output: Date; }
  JSON: { input: any; output: any; }
};

export type AllowanceClaim = {
  __typename?: 'AllowanceClaim';
  amount: Scalars['Float']['output'];
  from: Scalars['String']['output'];
  purpose: Scalars['String']['output'];
  receipts: Array<Scalars['String']['output']>;
  to: Scalars['String']['output'];
  workingHours: Scalars['Float']['output'];
};

export type Attendance = {
  __typename?: 'Attendance';
  _id: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type AuthOutput = {
  __typename?: 'AuthOutput';
  access_token: Scalars['String']['output'];
  user: User;
};

export type ClaimData = AllowanceClaim | ExpenseClaim | SiteClaim | TravelClaim;

export type ClaimDataInput = {
  amount: Scalars['Float']['input'];
  purpose: Scalars['String']['input'];
  receipts: Array<Scalars['String']['input']>;
};

export type ClaimInput = {
  claimData: ClaimDataInput;
  claimType: ClaimType;
};

export enum ClaimStatus {
  Approved = 'APPROVED',
  Paid = 'PAID',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export enum ClaimType {
  Allowance = 'ALLOWANCE',
  Expense = 'EXPENSE',
  Site = 'SITE',
  Travel = 'TRAVEL'
}

export type Claims = {
  __typename?: 'Claims';
  _id: Scalars['String']['output'];
  claimData: ClaimData;
  claimType: ClaimType;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  rejectedReason?: Maybe<Scalars['String']['output']>;
  status: ClaimStatus;
  updatedAt: Scalars['DateTime']['output'];
  user: User;
};

export type ClockInInput = {
  locationId: Scalars['String']['input'];
  shiftId: Scalars['String']['input'];
};

export type ClockOutInput = {
  attendanceId: Scalars['String']['input'];
};

export type CreateLeaveInput = {
  endDateTime: Scalars['DateTime']['input'];
  leaveType: LeaveType;
  reason: Scalars['String']['input'];
  startDateTime: Scalars['DateTime']['input'];
};

export type ExpenseClaim = {
  __typename?: 'ExpenseClaim';
  amount: Scalars['Float']['output'];
  date: Scalars['DateTime']['output'];
  items: Array<Scalars['String']['output']>;
  purpose: Scalars['String']['output'];
  receipts: Array<Scalars['String']['output']>;
};

export type FindLeavesInput = {
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<LeaveStatus>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type Leave = {
  __typename?: 'Leave';
  _id: Scalars['String']['output'];
  approvedAt?: Maybe<Scalars['DateTime']['output']>;
  approvedBy?: Maybe<User>;
  createdAt: Scalars['DateTime']['output'];
  endDateTime: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  leaveStatus: LeaveStatus;
  leaveType: LeaveType;
  reason: Scalars['String']['output'];
  rejectedReason?: Maybe<Scalars['String']['output']>;
  startDateTime: Scalars['DateTime']['output'];
  updatedAt: Scalars['DateTime']['output'];
  user: User;
};

export enum LeaveStatus {
  Approved = 'APPROVED',
  Cancelled = 'CANCELLED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export enum LeaveType {
  Annual = 'ANNUAL',
  Emergency = 'EMERGENCY',
  Maternity = 'MATERNITY',
  Paternity = 'PATERNITY',
  Sick = 'SICK',
  Unpaid = 'UNPAID'
}

export type Location = {
  __typename?: 'Location';
  _id: Scalars['String']['output'];
  address: Scalars['String']['output'];
  coordinates: Array<Scalars['Float']['output']>;
  createdAt: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  emergencyContact: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  clockIn: Attendance;
  clockOut: Attendance;
  createClaim: Claims;
  createLeave: Leave;
  signIn: AuthOutput;
};


export type MutationClockInArgs = {
  clockInInput: ClockInInput;
};


export type MutationClockOutArgs = {
  clockOutInput: ClockOutInput;
};


export type MutationCreateClaimArgs = {
  input: ClaimInput;
};


export type MutationCreateLeaveArgs = {
  createLeaveInput: CreateLeaveInput;
};


export type MutationSignInArgs = {
  input: SignInInput;
};

export type Query = {
  __typename?: 'Query';
  claims: Array<Claims>;
  leaves: Array<Leave>;
  locations: Array<Location>;
  me: User;
  shift: Shift;
  shifts: Array<Shift>;
  shiftsByUser: Array<Shift>;
};


export type QueryLeavesArgs = {
  filter: FindLeavesInput;
};


export type QueryShiftArgs = {
  id: Scalars['String']['input'];
};


export type QueryShiftsArgs = {
  shiftsInput: ShiftsInput;
};


export type QueryShiftsByUserArgs = {
  userId: Scalars['String']['input'];
};

export enum Role {
  Admin = 'ADMIN',
  Guard = 'GUARD',
  Manager = 'MANAGER',
  Supervisor = 'SUPERVISOR'
}

export type Shift = {
  __typename?: 'Shift';
  _id: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  endDateTime: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  isRecurring: Scalars['Boolean']['output'];
  location: Location;
  recurringId?: Maybe<Scalars['String']['output']>;
  startDateTime: Scalars['DateTime']['output'];
  updatedAt: Scalars['DateTime']['output'];
  users: Array<User>;
};

export type ShiftsInput = {
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  locationId?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type SignInInput = {
  password: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type Site = {
  __typename?: 'Site';
  _id: Scalars['String']['output'];
  address: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  emergencyContact: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type SiteClaim = {
  __typename?: 'SiteClaim';
  amount: Scalars['Float']['output'];
  items: Array<Scalars['String']['output']>;
  purpose: Scalars['String']['output'];
  receipts: Array<Scalars['String']['output']>;
  site: Site;
};

export type TravelClaim = {
  __typename?: 'TravelClaim';
  amount: Scalars['Float']['output'];
  client: Scalars['String']['output'];
  distance: Scalars['Float']['output'];
  from: Scalars['String']['output'];
  purpose: Scalars['String']['output'];
  receipts: Array<Scalars['String']['output']>;
  to: Scalars['String']['output'];
  toll: Scalars['Float']['output'];
};

export type User = {
  __typename?: 'User';
  _id: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  fullname: Scalars['String']['output'];
  id: Scalars['String']['output'];
  password: Scalars['String']['output'];
  phone: Scalars['String']['output'];
  role: Role;
  updatedAt: Scalars['DateTime']['output'];
  userStatus: UserStatus;
};

export enum UserStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE',
  Suspended = 'SUSPENDED'
}

export type SigninMutationVariables = Exact<{
  input: SignInInput;
}>;


export type SigninMutation = { __typename?: 'Mutation', signIn: { __typename?: 'AuthOutput', access_token: string } };

export type CreateClaimMutationVariables = Exact<{
  input: ClaimInput;
}>;


export type CreateClaimMutation = { __typename?: 'Mutation', createClaim: { __typename?: 'Claims', _id: string, id: string, createdAt: Date, updatedAt: Date, status: ClaimStatus, claimType: ClaimType, rejectedReason?: string | null, claimData: { __typename?: 'AllowanceClaim', amount: number, purpose: string, from: string, to: string, workingHours: number, receipts: Array<string> } | { __typename?: 'ExpenseClaim', amount: number, purpose: string, items: Array<string>, date: Date, receipts: Array<string> } | { __typename?: 'SiteClaim', amount: number, purpose: string, items: Array<string>, receipts: Array<string>, site: { __typename?: 'Site', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, description: string, address: string, emergencyContact: string } } | { __typename?: 'TravelClaim', amount: number, purpose: string, from: string, to: string, client: string, toll: number, distance: number, receipts: Array<string> } } };

export type CreateLeaveMutationVariables = Exact<{
  input: CreateLeaveInput;
}>;


export type CreateLeaveMutation = { __typename?: 'Mutation', createLeave: { __typename?: 'Leave', _id: string } };

export type GetLeavesQueryVariables = Exact<{
  input: FindLeavesInput;
}>;


export type GetLeavesQuery = { __typename?: 'Query', leaves: Array<{ __typename?: 'Leave', _id: string, id: string, createdAt: Date, updatedAt: Date, reason: string, leaveType: LeaveType, startDateTime: Date, endDateTime: Date, leaveStatus: LeaveStatus, rejectedReason?: string | null, user: { __typename?: 'User', _id: string, fullname: string, phone: string, userStatus: UserStatus, role: Role }, approvedBy?: { __typename?: 'User', _id: string, fullname: string, phone: string, userStatus: UserStatus, role: Role } | null }> };

export type LocationsQueryVariables = Exact<{ [key: string]: never; }>;


export type LocationsQuery = { __typename?: 'Query', locations: Array<{ __typename?: 'Location', id: string, name: string, description: string, address: string, emergencyContact: string }> };

export type MeQueryVariables = Exact<{ [key: string]: never; }>;


export type MeQuery = { __typename?: 'Query', me: { __typename?: 'User', _id: string, id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, role: Role, password: string } };

export type ShiftFragmentFragment = { __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location: { __typename?: 'Location', name: string, id: string }, users: Array<{ __typename?: 'User', fullname: string, id: string }> };

export type ShiftQueryVariables = Exact<{
  shiftId: Scalars['String']['input'];
}>;


export type ShiftQuery = { __typename?: 'Query', shift: { __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location: { __typename?: 'Location', name: string, id: string }, users: Array<{ __typename?: 'User', fullname: string, id: string }> } };

export type ShiftsQueryVariables = Exact<{
  input: ShiftsInput;
}>;


export type ShiftsQuery = { __typename?: 'Query', shifts: Array<{ __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location: { __typename?: 'Location', name: string, id: string }, users: Array<{ __typename?: 'User', fullname: string, id: string }> }> };

export type GetShiftsByUserQueryVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type GetShiftsByUserQuery = { __typename?: 'Query', shiftsByUser: Array<{ __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location: { __typename?: 'Location', name: string, id: string }, users: Array<{ __typename?: 'User', fullname: string, id: string }> }> };

export type ClockInMutationVariables = Exact<{
  clockInInput: ClockInInput;
}>;


export type ClockInMutation = { __typename?: 'Mutation', clockIn: { __typename?: 'Attendance', id: string } };

export type ClockOutMutationVariables = Exact<{
  clockOut: ClockOutInput;
}>;


export type ClockOutMutation = { __typename?: 'Mutation', clockOut: { __typename?: 'Attendance', id: string } };


export const ShiftFragmentFragmentDoc = `
    fragment ShiftFragment on Shift {
  id
  recurringId
  startDateTime
  endDateTime
  location {
    name
    id
  }
  users {
    fullname
    id
  }
}
    `;
export const SigninDocument = `
    mutation Signin($input: SignInInput!) {
  signIn(input: $input) {
    access_token
  }
}
    `;

export const useSigninMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<SigninMutation, TError, SigninMutationVariables, TContext>) => {
    
    return useMutation<SigninMutation, TError, SigninMutationVariables, TContext>(
      ['Signin'],
      (variables?: SigninMutationVariables) => fetchData<SigninMutation, SigninMutationVariables>(SigninDocument, variables)(),
      options
    )};

export const CreateClaimDocument = `
    mutation CreateClaim($input: ClaimInput!) {
  createClaim(input: $input) {
    _id
    id
    createdAt
    updatedAt
    status
    claimType
    claimData {
      ... on AllowanceClaim {
        amount
        purpose
        from
        to
        workingHours
        receipts
      }
      ... on TravelClaim {
        amount
        purpose
        from
        to
        client
        toll
        distance
        receipts
      }
      ... on ExpenseClaim {
        amount
        purpose
        items
        date
        receipts
      }
      ... on SiteClaim {
        amount
        purpose
        items
        receipts
        site {
          _id
          id
          createdAt
          updatedAt
          name
          description
          address
          emergencyContact
        }
      }
    }
    rejectedReason
  }
}
    `;

export const useCreateClaimMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateClaimMutation, TError, CreateClaimMutationVariables, TContext>) => {
    
    return useMutation<CreateClaimMutation, TError, CreateClaimMutationVariables, TContext>(
      ['CreateClaim'],
      (variables?: CreateClaimMutationVariables) => fetchData<CreateClaimMutation, CreateClaimMutationVariables>(CreateClaimDocument, variables)(),
      options
    )};

export const CreateLeaveDocument = `
    mutation CreateLeave($input: CreateLeaveInput!) {
  createLeave(createLeaveInput: $input) {
    _id
  }
}
    `;

export const useCreateLeaveMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateLeaveMutation, TError, CreateLeaveMutationVariables, TContext>) => {
    
    return useMutation<CreateLeaveMutation, TError, CreateLeaveMutationVariables, TContext>(
      ['CreateLeave'],
      (variables?: CreateLeaveMutationVariables) => fetchData<CreateLeaveMutation, CreateLeaveMutationVariables>(CreateLeaveDocument, variables)(),
      options
    )};

export const GetLeavesDocument = `
    query GetLeaves($input: FindLeavesInput!) {
  leaves(filter: $input) {
    _id
    id
    createdAt
    updatedAt
    reason
    leaveType
    startDateTime
    endDateTime
    leaveStatus
    rejectedReason
    user {
      _id
      fullname
      phone
      userStatus
      role
    }
    approvedBy {
      _id
      fullname
      phone
      userStatus
      role
    }
  }
}
    `;

export const useGetLeavesQuery = <
      TData = GetLeavesQuery,
      TError = Error
    >(
      variables: GetLeavesQueryVariables,
      options?: UseQueryOptions<GetLeavesQuery, TError, TData>
    ) => {
    
    return useQuery<GetLeavesQuery, TError, TData>(
      ['GetLeaves', variables],
      fetchData<GetLeavesQuery, GetLeavesQueryVariables>(GetLeavesDocument, variables),
      options
    )};

useGetLeavesQuery.document = GetLeavesDocument;

useGetLeavesQuery.getKey = (variables: GetLeavesQueryVariables) => ['GetLeaves', variables];

export const LocationsDocument = `
    query Locations {
  locations {
    id
    name
    description
    address
    emergencyContact
  }
}
    `;

export const useLocationsQuery = <
      TData = LocationsQuery,
      TError = Error
    >(
      variables?: LocationsQueryVariables,
      options?: UseQueryOptions<LocationsQuery, TError, TData>
    ) => {
    
    return useQuery<LocationsQuery, TError, TData>(
      variables === undefined ? ['Locations'] : ['Locations', variables],
      fetchData<LocationsQuery, LocationsQueryVariables>(LocationsDocument, variables),
      options
    )};

useLocationsQuery.document = LocationsDocument;

useLocationsQuery.getKey = (variables?: LocationsQueryVariables) => variables === undefined ? ['Locations'] : ['Locations', variables];

export const MeDocument = `
    query Me {
  me {
    _id
    id
    createdAt
    updatedAt
    fullname
    phone
    userStatus
    role
    password
  }
}
    `;

export const useMeQuery = <
      TData = MeQuery,
      TError = Error
    >(
      variables?: MeQueryVariables,
      options?: UseQueryOptions<MeQuery, TError, TData>
    ) => {
    
    return useQuery<MeQuery, TError, TData>(
      variables === undefined ? ['Me'] : ['Me', variables],
      fetchData<MeQuery, MeQueryVariables>(MeDocument, variables),
      options
    )};

useMeQuery.document = MeDocument;

useMeQuery.getKey = (variables?: MeQueryVariables) => variables === undefined ? ['Me'] : ['Me', variables];

export const ShiftDocument = `
    query Shift($shiftId: String!) {
  shift(id: $shiftId) {
    ...ShiftFragment
  }
}
    ${ShiftFragmentFragmentDoc}`;

export const useShiftQuery = <
      TData = ShiftQuery,
      TError = Error
    >(
      variables: ShiftQueryVariables,
      options?: UseQueryOptions<ShiftQuery, TError, TData>
    ) => {
    
    return useQuery<ShiftQuery, TError, TData>(
      ['Shift', variables],
      fetchData<ShiftQuery, ShiftQueryVariables>(ShiftDocument, variables),
      options
    )};

useShiftQuery.document = ShiftDocument;

useShiftQuery.getKey = (variables: ShiftQueryVariables) => ['Shift', variables];

export const ShiftsDocument = `
    query Shifts($input: ShiftsInput!) {
  shifts(shiftsInput: $input) {
    ...ShiftFragment
  }
}
    ${ShiftFragmentFragmentDoc}`;

export const useShiftsQuery = <
      TData = ShiftsQuery,
      TError = Error
    >(
      variables: ShiftsQueryVariables,
      options?: UseQueryOptions<ShiftsQuery, TError, TData>
    ) => {
    
    return useQuery<ShiftsQuery, TError, TData>(
      ['Shifts', variables],
      fetchData<ShiftsQuery, ShiftsQueryVariables>(ShiftsDocument, variables),
      options
    )};

useShiftsQuery.document = ShiftsDocument;

useShiftsQuery.getKey = (variables: ShiftsQueryVariables) => ['Shifts', variables];

export const GetShiftsByUserDocument = `
    query GetShiftsByUser($userId: String!) {
  shiftsByUser(userId: $userId) {
    ...ShiftFragment
  }
}
    ${ShiftFragmentFragmentDoc}`;

export const useGetShiftsByUserQuery = <
      TData = GetShiftsByUserQuery,
      TError = Error
    >(
      variables: GetShiftsByUserQueryVariables,
      options?: UseQueryOptions<GetShiftsByUserQuery, TError, TData>
    ) => {
    
    return useQuery<GetShiftsByUserQuery, TError, TData>(
      ['GetShiftsByUser', variables],
      fetchData<GetShiftsByUserQuery, GetShiftsByUserQueryVariables>(GetShiftsByUserDocument, variables),
      options
    )};

useGetShiftsByUserQuery.document = GetShiftsByUserDocument;

useGetShiftsByUserQuery.getKey = (variables: GetShiftsByUserQueryVariables) => ['GetShiftsByUser', variables];

export const ClockInDocument = `
    mutation ClockIn($clockInInput: ClockInInput!) {
  clockIn(clockInInput: $clockInInput) {
    id
  }
}
    `;

export const useClockInMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<ClockInMutation, TError, ClockInMutationVariables, TContext>) => {
    
    return useMutation<ClockInMutation, TError, ClockInMutationVariables, TContext>(
      ['ClockIn'],
      (variables?: ClockInMutationVariables) => fetchData<ClockInMutation, ClockInMutationVariables>(ClockInDocument, variables)(),
      options
    )};

export const ClockOutDocument = `
    mutation ClockOut($clockOut: ClockOutInput!) {
  clockOut(clockOutInput: $clockOut) {
    id
  }
}
    `;

export const useClockOutMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<ClockOutMutation, TError, ClockOutMutationVariables, TContext>) => {
    
    return useMutation<ClockOutMutation, TError, ClockOutMutationVariables, TContext>(
      ['ClockOut'],
      (variables?: ClockOutMutationVariables) => fetchData<ClockOutMutation, ClockOutMutationVariables>(ClockOutDocument, variables)(),
      options
    )};
