# Temporary schema file - replace with actual schema when backend is fixed
type Query {
  # Logged in user
  me: User!

  # Shifts
  shift(id: String!): Shift!
  shifts(shiftsInput: ShiftsInput!): [Shift!]!
  shiftsByUser(userId: String!): [Shift!]!

  # Locations
  locations: [Location!]!

  # Leave
  leaves(filter: FindLeavesInput!): [Leave!]!

  # Claims
  claims: [Claims!]!
}

type Mutation {
  # Authentication
  signIn(input: SignInInput!): AuthOutput!

  # Leave
  createLeave(createLeaveInput: CreateLeaveInput!): Leave!

  # Claims
  createClaim(input: ClaimInput!): Claims!

  # Attendance
  clockIn(clockInInput: ClockInInput!): Attendance!
  clockOut(clockOutInput: ClockOutInput!): Attendance!
}

type User {
  _id: String!
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  fullname: String!
  phone: String!
  userStatus: UserStatus!
  role: Role!
  password: String!
}

type Shift {
  _id: String!
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  startDateTime: DateTime!
  endDateTime: DateTime!
  location: Location!
  users: [User!]!
  isRecurring: Boolean!
  recurringId: String
}

type Location {
  _id: String!
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  name: String!
  description: String!
  address: String!
  emergencyContact: String!
  coordinates: [Float!]!
}

type Leave {
  _id: String!
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  user: User!
  leaveType: LeaveType!
  startDateTime: DateTime!
  endDateTime: DateTime!
  reason: String!
  leaveStatus: LeaveStatus!
  rejectedReason: String
  approvedBy: User
  approvedAt: DateTime
}

type Claims {
  _id: String!
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  user: User!
  status: ClaimStatus!
  claimType: ClaimType!
  claimData: ClaimData!
  rejectedReason: String
}

type Attendance {
  _id: String!
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

union ClaimData = AllowanceClaim | TravelClaim | ExpenseClaim | SiteClaim

type AllowanceClaim {
  amount: Float!
  purpose: String!
  from: String!
  to: String!
  workingHours: Float!
  receipts: [String!]!
}

type TravelClaim {
  amount: Float!
  purpose: String!
  from: String!
  to: String!
  client: String!
  toll: Float!
  distance: Float!
  receipts: [String!]!
}

type ExpenseClaim {
  amount: Float!
  purpose: String!
  items: [String!]!
  date: DateTime!
  receipts: [String!]!
}

type SiteClaim {
  amount: Float!
  purpose: String!
  items: [String!]!
  receipts: [String!]!
  site: Site!
}

type Site {
  _id: String!
  id: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  name: String!
  description: String!
  address: String!
  emergencyContact: String!
}

type AuthOutput {
  access_token: String!
  user: User!
}

input SignInInput {
  phone: String!
  password: String!
}

input CreateLeaveInput {
  leaveType: LeaveType!
  startDateTime: DateTime!
  endDateTime: DateTime!
  reason: String!
}

input ClaimInput {
  claimType: ClaimType!
  claimData: ClaimDataInput!
}

input ClaimDataInput {
  amount: Float!
  purpose: String!
  receipts: [String!]!
}

input ShiftsInput {
  locationId: String
  userId: String
  startDate: DateTime
  endDate: DateTime
}

input FindLeavesInput {
  userId: String
  status: LeaveStatus
  startDate: DateTime
  endDate: DateTime
}

input ClockInInput {
  shiftId: String!
  locationId: String!
}

input ClockOutInput {
  attendanceId: String!
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum Role {
  ADMIN
  MANAGER
  GUARD
  SUPERVISOR
}

enum LeaveType {
  ANNUAL
  SICK
  EMERGENCY
  MATERNITY
  PATERNITY
  UNPAID
}

enum LeaveStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum ClaimStatus {
  PENDING
  APPROVED
  REJECTED
  PAID
}

enum ClaimType {
  ALLOWANCE
  TRAVEL
  EXPENSE
  SITE
}

scalar DateTime
scalar JSON
