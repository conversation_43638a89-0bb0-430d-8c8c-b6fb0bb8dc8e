#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const args = process.argv.slice(2);
const mode = args[0];

if (!mode || !['live', 'temp'].includes(mode)) {
  console.log('Usage: node scripts/switch-codegen.js [live|temp]');
  console.log('');
  console.log('  live - Use live GraphQL server');
  console.log('  temp - Use temporary schema file');
  process.exit(1);
}

const codegenPath = path.join(__dirname, '..', 'codegen.ts');
const liveConfigPath = path.join(__dirname, '..', 'codegen.live.ts');

try {
  if (mode === 'live') {
    if (!fs.existsSync(liveConfigPath)) {
      console.error('Error: codegen.live.ts not found');
      process.exit(1);
    }
    
    // Copy live config to main config
    const liveConfig = fs.readFileSync(liveConfigPath, 'utf8');
    fs.writeFileSync(codegenPath, liveConfig);
    
    console.log('✅ Switched to live GraphQL server configuration');
    console.log('📡 Make sure your backend server is running on http://localhost:4000/graphql');
    console.log('🔄 Run: npm run codegen');
    
  } else if (mode === 'temp') {
    // Create temp config
    const tempConfig = `import { CodegenConfig } from "@graphql-codegen/cli";

const config: CodegenConfig = {
  // Temporarily disabled until backend DataLoader issue is fixed
  // schema: "http://localhost:4000/graphql",
  schema: "./schema.graphql", // Will be created when backend is fixed
  documents: ["./graphql/**/*.gql"],
  ignoreNoDocuments: true, // for better experience with the watcher
  generates: {
    "./generated/graphql.ts": {
      plugins: [
        "typescript",
        "typescript-operations",
        "typescript-react-query",
      ],
      config: {
        isReactHook: false,
        fetcher: "../client#fetchData",
        errorType: "Error",
        exposeDocument: true,
        exposeQueryKeys: true,
        scalars: {
          DateTime: "Date",
          JSON: "any",
        },
      },
    },
  },
};

export default config;
`;
    
    fs.writeFileSync(codegenPath, tempConfig);
    
    console.log('✅ Switched to temporary schema configuration');
    console.log('📄 Using schema.graphql file');
    console.log('🔄 Run: npm run codegen');
  }
  
} catch (error) {
  console.error('Error switching configuration:', error.message);
  process.exit(1);
}
