const fs = require('fs');
const { getIntrospectionQuery, buildClientSchema, printSchema } = require('graphql');

async function getSchema() {
  try {
    const response = await fetch('http://localhost:4000/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: getIntrospectionQuery(),
      }),
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('GraphQL errors:', result.errors);
      process.exit(1);
    }

    // Save introspection result as JSON
    fs.writeFileSync('./schema.json', JSON.stringify(result.data, null, 2));
    
    // Also save as SDL for reference
    const schema = buildClientSchema(result.data);
    const sdl = printSchema(schema);
    fs.writeFileSync('./schema.graphql', sdl);
    
    console.log('Schema files created successfully!');
    console.log('- schema.json (for codegen)');
    console.log('- schema.graphql (SDL for reference)');
    
  } catch (error) {
    console.error('Failed to fetch schema:', error.message);
    process.exit(1);
  }
}

getSchema();
